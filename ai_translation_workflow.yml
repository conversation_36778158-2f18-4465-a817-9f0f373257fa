app:
  description: "专门用于人工智能领域的英文PDF翻译为中文PDF的工作流程，支持专业术语翻译和格式保持"
  icon: "🤖"
  icon_background: '#FFEAD5'
  mode: workflow
  name: "AI英译中PDF翻译器"
  use_icon_as_answer_icon: false

kind: app
version: 0.1.0

workflow:
  conversation_variables: []
  environment_variables: []
  
  features:
    file_upload:
      allowed_file_extensions:
        - ".pdf"
        - ".PDF"
      allowed_file_types:
        - "document"
      allowed_file_upload_methods:
        - "local_file"
        - "remote_url"
      enabled: true
      fileUploadConfig:
        file_size_limit: 50
        workflow_file_upload_limit: 1
      number_limits: 1
    
    opening_statement: "请上传您需要翻译的英文PDF文件，我将为您提供专业的AI领域中文翻译。"
    
    retriever_resource:
      enabled: false
    
    sensitive_word_avoidance:
      enabled: false
    
    speech_to_text:
      enabled: false
    
    suggested_questions:
      - "如何上传PDF文件进行翻译？"
      - "翻译过程中会保持原文格式吗？"
      - "支持哪些AI专业术语的翻译？"
    
    suggested_questions_after_answer:
      enabled: false
    
    text_to_speech:
      enabled: false

  graph:
    # 工作流程节点连接关系
    edges:
      - data:
          isInIteration: false
          sourceType: start
          targetType: code
        id: start-extract-target
        source: start_node
        sourceHandle: source
        target: pdf_extract_node
        targetHandle: target
        type: custom
        zIndex: 0
      
      - data:
          isInIteration: false
          sourceType: code
          targetType: code
        id: extract-chunk-target
        source: pdf_extract_node
        sourceHandle: source
        target: text_chunk_node
        targetHandle: target
        type: custom
        zIndex: 0
      
      - data:
          isInIteration: false
          sourceType: code
          targetType: iteration
        id: chunk-iteration-target
        source: text_chunk_node
        sourceHandle: source
        target: translation_iteration
        targetHandle: target
        type: custom
        zIndex: 0
      
      # 迭代内部连接
      - data:
          isInIteration: true
          iteration_id: translation_iteration
          sourceType: iteration-start
          targetType: llm
        id: iteration-start-terminology-target
        source: translation_iteration_start
        sourceHandle: source
        target: terminology_extraction
        targetHandle: target
        type: custom
        zIndex: 1002
      
      - data:
          isInIteration: true
          iteration_id: translation_iteration
          sourceType: llm
          targetType: llm
        id: terminology-initial-target
        source: terminology_extraction
        sourceHandle: source
        target: initial_translation
        targetHandle: target
        type: custom
        zIndex: 1002
      
      - data:
          isInIteration: true
          iteration_id: translation_iteration
          sourceType: llm
          targetType: llm
        id: initial-review-target
        source: initial_translation
        sourceHandle: source
        target: translation_review
        targetHandle: target
        type: custom
        zIndex: 1002
      
      - data:
          isInIteration: true
          iteration_id: translation_iteration
          sourceType: llm
          targetType: llm
        id: review-final-target
        source: translation_review
        sourceHandle: source
        target: final_translation
        targetHandle: target
        type: custom
        zIndex: 1002
      
      # 后续处理连接
      - data:
          isInIteration: false
          sourceType: iteration
          targetType: template-transform
        id: iteration-template-target
        source: translation_iteration
        sourceHandle: source
        target: text_aggregation
        targetHandle: target
        type: custom
        zIndex: 0
      
      - data:
          isInIteration: false
          sourceType: template-transform
          targetType: code
        id: template-pdf-target
        source: text_aggregation
        sourceHandle: source
        target: pdf_generation
        targetHandle: target
        type: custom
        zIndex: 0
      
      - data:
          isInIteration: false
          sourceType: code
          targetType: end
        id: pdf-end-target
        source: pdf_generation
        sourceHandle: source
        target: end_node
        targetHandle: target
        type: custom
        zIndex: 0

    # 工作流程节点定义
    nodes:
      # 开始节点
      - data:
          desc: "上传英文PDF文件开始翻译流程"
          selected: false
          title: "开始"
          type: start
          variables:
            - allowed_file_extensions:
                - ".pdf"
                - ".PDF"
              allowed_file_types:
                - "document"
              allowed_file_upload_methods:
                - "local_file"
                - "remote_url"
              label: "英文PDF文件"
              required: true
              type: file
              variable: input_pdf
        height: 90
        id: start_node
        position:
          x: 30
          y: 300
        positionAbsolute:
          x: 30
          y: 300
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # PDF文本提取节点
      - data:
          code: |
            import PyPDF2
            import io
            import re

            def main(input_pdf):
                try:
                    # 从上传的文件中提取文本
                    pdf_content = input_pdf['content']
                    pdf_file = io.BytesIO(pdf_content)
                    
                    # 使用PyPDF2读取PDF
                    pdf_reader = PyPDF2.PdfReader(pdf_file)
                    
                    extracted_text = ""
                    page_info = []
                    
                    for page_num, page in enumerate(pdf_reader.pages):
                        page_text = page.extract_text()
                        if page_text.strip():  # 只处理非空页面
                            extracted_text += f"\n\n--- Page {page_num + 1} ---\n\n"
                            extracted_text += page_text
                            page_info.append({
                                "page_number": page_num + 1,
                                "text_length": len(page_text)
                            })
                    
                    # 清理文本格式
                    cleaned_text = re.sub(r'\n\s*\n', '\n\n', extracted_text)
                    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
                    
                    return {
                        "extracted_text": cleaned_text,
                        "total_pages": len(pdf_reader.pages),
                        "page_info": str(page_info),
                        "text_length": len(cleaned_text)
                    }
                    
                except Exception as e:
                    return {
                        "extracted_text": f"PDF提取失败: {str(e)}",
                        "total_pages": 0,
                        "page_info": "[]",
                        "text_length": 0
                    }
          code_language: python3
          dependencies:
            - PyPDF2
          desc: "从上传的PDF文件中提取文本内容"
          outputs:
            extracted_text:
              children: null
              type: string
            total_pages:
              children: null
              type: number
            page_info:
              children: null
              type: string
            text_length:
              children: null
              type: number
          selected: false
          title: "PDF文本提取"
          type: code
          variables:
            - value_selector:
                - start_node
                - input_pdf
              variable: input_pdf
        height: 98
        id: pdf_extract_node
        position:
          x: 334
          y: 300
        positionAbsolute:
          x: 334
          y: 300
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

    viewport:
      x: 0
      y: 0
      zoom: 0.5
