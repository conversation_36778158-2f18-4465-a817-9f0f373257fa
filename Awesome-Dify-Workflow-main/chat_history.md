### wxid_2zyfe21ojzzw22 - 2025-05-08 11:22:08
我想请问一下dify在做知识库查询的时候，预处理文档有没有比较好的一个方法奥

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:22:22
![image](images/211746674542_.pic.jpg)

---

### hjlarry - 2025-05-08 11:22:40
其实就是常见的dify对一些文档的解析不友好？

---

### wxid_7da5db36yiad22 - 2025-05-08 11:22:40
哈哈哈

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:22:48
比如像这些pdf里面有表格，对啊

---

### wxid_7da5db36yiad22 - 2025-05-08 11:22:52
早上我自己搭了一个做知识库的

---

### wxid_7da5db36yiad22 - 2025-05-08 11:23:03
找到了一个开源项目

---

### wxid_7da5db36yiad22 - 2025-05-08 11:23:10
不对 主要是数据集的

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:24:32
奥奥，就是得先预处理一下嘛，然后在入库嘛

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:24:56
![image](images/301746674696_.pic.jpg)

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:25:06
刚才我还找了一个这个 不知道解析的好不好

---

### hjlarry - 2025-05-08 11:25:11
是的 入库最好是markdown格式的   要想省事  就是直接外接ragflow  我觉得是最省事的了

---

### wxid_7da5db36yiad22 - 2025-05-08 11:25:33
在弄ragflow比较好

---

### wxid_671txdjl47aq21 - 2025-05-08 11:29:18
现在在作第一步，word-》归纳分级总结。遇到的问题就是1、长文本怎么输出2、总结的点怎么能准确一些

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:30:00
他不会自带这个ocr识别这些吧

---

### wxid_671txdjl47aq21 - 2025-05-08 11:31:40
不固定，估计4W字内

---

### hjlarry - 2025-05-08 11:34:09
你这个和模型本身的能力强相关，我会先去尝试在线的gemini系列的模型  它是支持最多上下文的一种模型    先看看最好的模型能不能把它处理的达到你要的总结效果

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:34:22
![image](images/451746675262_.pic.jpg)

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:34:48
这个牛的，确实[坏笑]。我感觉还是外接一个ragflow比较省事。

---

### Ron - 2025-05-08 11:34:50
@air 你的word，有示例可以发个看看么

---

### hjlarry - 2025-05-08 11:34:55
对的

---

### wxid_7da5db36yiad22 - 2025-05-08 11:35:48
![image](images/491746675348_.pic.jpg)

---

### wxid_7da5db36yiad22 - 2025-05-08 11:35:52
电脑随时都有

---

### wxid_7da5db36yiad22 - 2025-05-08 11:35:54
哈哈哈

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:37:53
[旺柴]可以的

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:38:03
看介绍确实香

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:38:19
他这个服务器上只有cpu应该也可以运行吧

---

### hjlarry - 2025-05-08 11:38:50
CPU可以   ragflow是比较费资源的

---

### wxid_7da5db36yiad22 - 2025-05-08 11:38:51
也要跑大模型

---

### wxid_7da5db36yiad22 - 2025-05-08 11:39:05
估计就只能跑小的

---

### wxid_7da5db36yiad22 - 2025-05-08 11:39:24
你们ragflow用什么embedding模型啊

---

### hjlarry - 2025-05-08 11:42:11
embedding个人感觉都差不太多 [捂脸]

---

### wxid_7da5db36yiad22 - 2025-05-08 11:43:17
这样子吗

---

### Ron - 2025-05-08 11:57:35
我看看

---

### wxid_2zyfe21ojzzw22 - 2025-05-08 11:59:04
我感觉你这个第一步应该是结构化，先转成markdown好些，然后针对每一段进行摘要分析

---

### Ron - 2025-05-08 12:02:22
![image](images/671746676942_.pic.jpg)

---

### Ron - 2025-05-08 12:02:53
把这些，通过正则匹配，加上一个【父级】这样类似的标记位

---

### Ron - 2025-05-08 12:03:07
![image](images/691746676987_.pic.jpg)

---

### wwzhouhui - 2025-05-08 12:03:10
ragflow比较费资源，而且处理速算会比较慢

---

### Ron - 2025-05-08 12:03:25
再把这样开头的，添加一个【子段落】标记位

---

### Ron - 2025-05-08 12:05:10
![image](images/721746677110_.pic.jpg)

---

### Ron - 2025-05-08 12:05:45
![image](images/731746677145_.pic.jpg)

---

### Ron - 2025-05-08 12:06:02
![image](images/741746677162_.pic.jpg)

---

### Ron - 2025-05-08 12:06:16
类似这样，处理这个word，然后再父子方式放入知识库

---

### wxid_671txdjl47aq21 - 2025-05-08 12:10:27
放入知识库是为了分段吗，正常模型有这功能吗

---

### wxid_671txdjl47aq21 - 2025-05-08 12:12:02
放入知识库后，后面如何生成整篇excle

---

### hjlarry - 2025-05-08 12:13:26
大语言模型的核心瓶颈是不支持长上下文，放入知识库分段是为了解决具体问题时只找到部分相关知识，这样模型就处理的更好

---

### wwzhouhui - 2025-05-08 12:35:16
这个简单的doc转md文件参考代码

---

### wwzhouhui - 2025-05-08 12:39:33
复杂的可以用mineru

---

### wxid_9pgkq8ic90d521 - 2025-05-08 13:01:32
如果我想让ai读docx，然后处理数据，输出保留原来docx的格式，有什么方法吗，docx里有表格有颜色，有页眉页脚

---

### hjlarry - 2025-05-08 13:02:14
你的意思是ai输出的内容  要写入到原有的docx相应的地方上去？

---

### wxid_9pgkq8ic90d521 - 2025-05-08 13:02:58
或者重新组织docx，和原有的格式保持一致，你说的修改对应位置也可以

---

### Ron - 2025-05-08 13:03:46
感觉，这不是 ai 做的，是代码做的

---

### Ron - 2025-05-08 13:04:01
https://dingyu.me/blog/lessons-translator-app-beats-google-translate-deepl

---

### Ron - 2025-05-08 13:04:24
感觉像这个人做的事

---

### hjlarry - 2025-05-08 13:05:14
这个需要你自己写插件了    是一个工程上处理的事情

---

### hjlarry - 2025-05-08 13:05:47
ai只知道生成内容   但是docx不属于markdown那种通用格式   需要你自己做内容的替换

---

### wxid_9pgkq8ic90d521 - 2025-05-08 13:06:20
那有没有方法docx转markdown保留了全部格式标记的脚本，然后还可以转换回来

---

### hjlarry - 2025-05-08 13:09:01
不会的  markdown支持的格式标记是很少的   那势必转换就会丢失格式

---

### wwzhouhui - 2025-05-08 13:43:43
PDF Craft 可以将 PDF 文件转化为各种其他格式。该项目将专注于扫描书籍的 PDF 文件的处理

https://github.com/oomol-lab/pdf-craft/blob/main/README_zh-CN.md

---

### wwzhouhui - 2025-05-08 13:43:55
这个可以保持原来的格式

---

### Ron - 2025-05-08 14:10:05
[抱拳][抱拳][抱拳]

---

### wxid_9pgkq8ic90d521 - 2025-05-08 14:31:38
如果我想做一个月的新闻总结，我的思路是爬下来这些新闻，然后放到知识库中，假设每天有10条新闻，我导入后尝试总结，感觉知识库召回的文章只召回了部分给ai去总结，想问下这种有什么思路解决

---

### hjlarry - 2025-05-08 14:34:08
知识库召回是根据你的关键字去匹配找对应的文章  不会去找全量文章的 
可以考虑先对每篇新闻总结一下 存数据库   然后月末了把库里的都拿出来给ai去总结

---

### wxid_9pgkq8ic90d521 - 2025-05-08 14:34:51
有道理

---

### wxid_9pgkq8ic90d521 - 2025-05-08 14:40:48
如果我是按月来做一个总结，那我是不是应该按月建知识库，如果是按月建知识库，那我岂不是每月都要调整工作流中挂的知识库的名称，有什么自动化可以处理的方法吗

---

### hjlarry - 2025-05-08 14:46:47
如果你用知识检索这个节点的话   它是不支持动态检索不同的知识库的   如果每个月的总结都在不同知识库  看起来是得经常调整

---

### Ron - 2025-05-08 14:49:16
感觉放一个知识库，贴标签

---

### Ron - 2025-05-08 14:49:19
是不是好点

---

### wxid_7da5db36yiad22 - 2025-05-08 14:51:09
好很多了

---

### wxid_9pgkq8ic90d521 - 2025-05-08 14:52:21
这个思路挺好[ThumbsUp]

---

### Ron - 2025-05-08 14:56:37
哈哈哈，vip 群开启专家模式，讨论一下，都有个方案

---

### wxid_9pgkq8ic90d521 - 2025-05-08 15:35:57
我看微信小程序的微搭低代码平台有说可以对接dify，这部分有相关demo或者案例可以学习下吗？最好是能传图片的[Salute]

---

### Ron - 2025-05-08 15:50:26
这个我没有，感觉都还是自己开发调接口为主

---

### wxid_pgye6v6ofbws22 - 2025-05-08 17:03:47
有哪些私有工作流呀

---

### wxid_twh09qctjv7822 - 2025-05-08 18:06:11
对话 agent 插件，它的实现逻辑在代码里边都有吗？
有个有点复杂的需求，跟这个插件的功能还挺类似

---

### wxid_pgye6v6ofbws22 - 2025-05-08 18:15:48
![image](images/1211746699348_.pic.jpg)

---

### wxid_pgye6v6ofbws22 - 2025-05-08 18:15:51
我注意到 dify-plugin-daemon 生成的文件类型是 blob_chunk，但 Dify 主项目目前只支持 blob 类型的处理。请问是否可以将插件返回的类型由 blob_chunk 修改为 blob

---

### wxid_pgye6v6ofbws22 - 2025-05-08 18:16:41
二开后的所有markdown转word插件都变成了blob_chunk导致用不了，不知道什么原因

---

### hjlarry - 2025-05-08 18:17:54
这个 blob chunk 是最近才加的，但是改动较大，需要 dify 本身，plugin 容器和 sdk 都更新，要么就都不更新也可以

---

### hjlarry - 2025-05-08 18:18:45
它解决的问题是大一点的多模态文件传输给大模型，导致卡死不返回

---

### wxid_pgye6v6ofbws22 - 2025-05-08 18:25:40
这样，那解决办法就是都更新

---

### hjlarry - 2025-05-08 18:26:57
对，要么都更要么都不更，比单独调一个好点

---

### hjlarry - 2025-05-08 20:08:01
@Frigo 我记错了，最近加的是 LLMResultChunk 啊，这个 blob chunk 是一直都有的，你具体遇到什么错误了吗？

---

### ani424094144 - 2025-05-08 20:20:33
目前的工作流模板都在应用模版里面是吗

---

### ani424094144 - 2025-05-08 20:20:33
![image](images/1321746706833_.pic.jpg)

---

### ani424094144 - 2025-05-08 20:20:59
还有哪里有散落的 dsl 文件哇？

---

### wxid_pgye6v6ofbws22 - 2025-05-08 20:24:45
官网的是blob

---

### wxid_pgye6v6ofbws22 - 2025-05-08 20:24:56
先更新代码看看

---

### wwzhouhui - 2025-05-08 20:27:36
https://github.com/svcvit/Awesome-Dify-Workflow 这个里面有非官方 最全的 dsl 

---

### ani424094144 - 2025-05-08 20:27:58
好的我来看看

---

### wwzhouhui - 2025-05-08 20:28:10
https://github.com/wwwzhouhui/dify-for-dsl 这个是我开源的项目 目前也是算比较全面的。

---

### wxid_pgye6v6ofbws22 - 2025-05-08 20:30:51
明天代码全部合并更新到最新版本再看看

---

### wxid_9pgkq8ic90d521 - 2025-05-09 10:16:46
通过http的方式访问dify的工作流在哪里可以看到调用请求及运行结果吗？界面上可以看到还是服务器后台哪个日志可以看到？

---

### hjlarry - 2025-05-09 10:18:09
日志在api容器里

---

### wxid_9pgkq8ic90d521 - 2025-05-09 10:20:48
能举例看看如何查看吗？

---

### hjlarry - 2025-05-09 10:21:50
你是全部docker compose启动的吗

---

### Ron - 2025-05-09 10:21:58
![image](images/1481746757318_.pic.jpg)

---

### Ron - 2025-05-09 10:22:11
我猜他说的会不会是这里的日志

---

### hjlarry - 2025-05-09 10:22:17
[捂脸]

---

### wxid_7da5db36yiad22 - 2025-05-09 10:23:33
[旺柴]搞一个开源项目 查看docker里面容器日志监控呗 搞一个页面

---

### Ron - 2025-05-09 10:27:23
http调用也会产生的

---

### wxid_9pgkq8ic90d521 - 2025-05-09 10:27:31
好的

---

### Ron - 2025-05-09 10:27:44
只有在编辑工作流的模式下，测试的时候不会产生

---

### Ron - 2025-05-09 10:28:04
![image](images/1581746757684_.pic.jpg)

---

### wxid_7da5db36yiad22 - 2025-05-09 10:28:15
[旺柴]安装我的思路 是不是日志看起来更加容易呢

---

### wxid_7da5db36yiad22 - 2025-05-09 10:29:21
[旺柴]哈哈哈 我容器比较多

---

### wxid_7da5db36yiad22 - 2025-05-09 10:31:26
2分钟拉了一个 超快

---

### wxid_7da5db36yiad22 - 2025-05-09 10:31:39
![image](images/1641746757899_.pic.jpg)

---

### wwzhouhui - 2025-05-09 10:42:57
我可以推荐一个给你

---

### wxid_7da5db36yiad22 - 2025-05-09 10:43:12
还有其他好用的？

---

### wxid_7da5db36yiad22 - 2025-05-09 10:43:20
感觉这个也可以了

---

### wwzhouhui - 2025-05-09 10:43:53
应该比你这个强

---

### wxid_7da5db36yiad22 - 2025-05-09 10:44:12
大神们 分享几个牛逼的dify工作流呗

---

### wwzhouhui - 2025-05-09 10:46:41
https://1panel.cn/docs/installation/online_installation/ 平台支持在线和离线安装

---

### wwzhouhui - 2025-05-09 10:46:54
这个工具应该比你工具好用

---

### wwzhouhui - 2025-05-09 10:47:25
不光监控容器，还有linux服务器都是可以的，最近还有mcp服务

---

### wxid_7da5db36yiad22 - 2025-05-09 10:47:49
[笑脸]好的 收藏 收藏

---

### wwzhouhui - 2025-05-09 10:48:39
后面也会有付费会员专享技术文章

---

### wwzhouhui - 2025-05-09 10:50:16
如果企业级一般用ki8s,如果规模小单个服务器推荐用1panel做服务器和容器管理

---

### wxid_7da5db36yiad22 - 2025-05-09 10:52:44
K8S目前没环境玩哦  哈哈哈

---

### wxid_9pgkq8ic90d521 - 2025-05-09 11:44:43
当前dify的api访问是http的，可以加证书变成https吗，具体在哪里配置？

---

### Ron - 2025-05-09 11:48:17
![image](images/1801746762497_.pic.jpg)

---

### Ron - 2025-05-09 11:48:35
![image](images/1811746762515_.pic.jpg)

---

### Ron - 2025-05-09 11:48:39
看这俩地方

---

### wxid_pgye6v6ofbws22 - 2025-05-09 14:08:29
golang的代码报错缺失SDK：2025-05-09 11:48:51.296] [ERROR] [35c65ed4-c9e6-4baf-9347-3017a9af6a6c] @local_runtime/environment_python. go:363 failed to get the version of the plugin sdk: failed to find the version of the plugin sdk

---

### wxid_pgye6v6ofbws22 - 2025-05-09 14:09:20
这个报错会影响到正常使用吗？目前没发现问题

---

### hjlarry - 2025-05-09 14:12:40
没遇到过  我来看看

---

### hjlarry - 2025-05-09 14:16:30
这个不影响  它只检测一下plugin sdk的版本

---

### hjlarry - 2025-05-09 14:16:30
![image](images/1891746771390_.pic.jpg)

---

### hjlarry - 2025-05-09 14:16:42
升级以后昨天的问题好了？

---

### wxid_pgye6v6ofbws22 - 2025-05-09 14:16:59
后端升级完是可以了

---

### hjlarry - 2025-05-09 14:17:55
[捂脸]

---

### wxid_pgye6v6ofbws22 - 2025-05-10 11:19:43
![image](images/1951746847183_.pic.jpg)

---

### wxid_pgye6v6ofbws22 - 2025-05-10 11:19:52
http插件限制了大小吗？

---

### wxid_pgye6v6ofbws22 - 2025-05-10 11:20:03
调用生图接口直接干报错了

---

### hjlarry - 2025-05-10 11:44:57
对的，环境变量里好像叫 http max body 吧


---

### godspeed6633 - 2025-05-11 11:45:03
求助  这个工具怎么添加？ 按DIFY操作手册 报错

---

### wwzhouhui - 2025-05-11 11:45:37
换成 局域网IP试一试 

---

### wwzhouhui - 2025-05-11 11:45:46
另外这插件需要 科学上网 

---

### wwzhouhui - 2025-05-11 11:46:57
https://mp.weixin.qq.com/s/49psBbYxd6oVdu6Q--hIbg

---

### wwzhouhui - 2025-05-11 11:47:14
我之前在0.15.3 版本测试过的 

---

### hjlarry - 2025-05-11 11:50:04
 0.15 版本，我把它的配置放在项目里了，所以直接启动就可以，但是 1.0 以后得自己启动了，需要自己设置了

---

### godspeed6633 - 2025-05-11 11:52:50
非常感谢

---

### wxid_pgye6v6ofbws22 - 2025-05-11 14:35:36
chatflow能像coze一样分段输出多个消息吗

---

### hjlarry - 2025-05-11 14:48:04
没怎么用 coze，什么样就算是分段输出

---

### wxid_pgye6v6ofbws22 - 2025-05-11 14:52:57
![image](images/3381746946377_.pic.jpg)

---

### wxid_pgye6v6ofbws22 - 2025-05-11 14:53:28
就像这样 回复不止一个

---

### hjlarry - 2025-05-11 15:04:27
这应该实现不了，它总是会把一个 chatflow 执行完

---

### wxid_pgye6v6ofbws22 - 2025-05-11 17:28:35
@RON 微信群聊二维码只能7天，你是怎么维护的？真手动替换吗 还是用了活码

---

### Ron - 2025-05-11 18:05:16
我是纯手工，本来也要维护的

---

### wwzhouhui - 2025-05-11 22:35:06
https://github.com/xxnuo/serverless-qrcode-hub   这个项目可以实现微信群聊二维码自动生成

---

### wxid_pgye6v6ofbws22 - 2025-05-11 23:57:33
🐮

---

### wwzhouhui - 2025-05-12 09:32:49
整理目前ron开源和我的dify工作流，放到百度网盘里面，需要的同学自取

---

### Ron - 2025-05-12 09:47:19
[ThumbsUp][ThumbsUp]

---

### ani424094144 - 2025-05-12 09:57:03
[ThumbsUp][ThumbsUp]

---

### wxid_twh09qctjv7822 - 2025-05-12 09:58:05
[强][强]

---

### godspeed6633 - 2025-05-12 10:03:22
非常好 正在体验

---

### wxid_pgye6v6ofbws22 - 2025-05-12 10:07:37
[强][强]

---

### godspeed6633 - 2025-05-12 10:07:55
ppt生成助手，依赖的容器没标识[流泪]

---

### godspeed6633 - 2025-05-12 10:07:56
![image](images/3541747015676_.pic.jpg)

---

### wxid_pgye6v6ofbws22 - 2025-05-12 10:13:04
这是本地服务

---

### dezhi-2008 - 2025-05-12 17:40:13
请问dify1.2以上的版本可以多agent协作吗

---

### hjlarry - 2025-05-12 17:40:58
可以设置多个agent节点  但是工作流都是向前  不能往回走  和multi agent有区别

---

### dezhi-2008 - 2025-05-12 17:41:18
哦哦好的，谢谢

---

### wxid_9pgkq8ic90d521 - 2025-05-12 17:42:49
![image](images/3611747042969_.pic.jpg)

---

### wxid_9pgkq8ic90d521 - 2025-05-12 17:43:29
duckduckgo这里报错，请问是什么原因呢？

---

### hjlarry - 2025-05-12 17:44:09
应该是这个插件的bug

---

