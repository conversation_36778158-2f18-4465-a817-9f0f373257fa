app:
  description: ''
  icon: "\U0001F916"
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: "\u601D\u8003\u52A9\u624B"
kind: app
version: 0.1.0
workflow:
  environment_variables: []
  features:
    file_upload:
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: llm
        targetType: answer
      id: llm-answer
      source: llm
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: start
        targetType: knowledge-retrieval
      id: 1723188302358-source-1723188386218-target
      source: '1723188302358'
      sourceHandle: source
      target: '1723188386218'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1723188386218-source-llm-target
      source: '1723188386218'
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: "\u5F00\u59CB"
        type: start
        variables: []
      height: 54
      id: '1723188302358'
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1723188386218'
          - result
        desc: ''
        memory:
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: yi-large-turbo
          provider: yi
        prompt_template:
        - id: f8d5d261-7634-482e-a096-d09737c03c9d
          role: system
          text: "\u4F60\u662F\u4E00\u4E2A\u5206\u6790\u4E13\u5BB6\uFF0C\u8BF7\u4F60\
            \u6839\u636E{{#sys.query#}}\u7684\u8981\u6C42\uFF0C\u4F7F\u7528{{#context#}}\u9700\
            \u8981\u7684\u6846\u67B6\uFF0C\u89E3\u51B3\u95EE\u9898"
        selected: false
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: llm
      position:
        x: 680
        y: 282
      positionAbsolute:
        x: 680
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#llm.text#}}'
        desc: ''
        selected: false
        title: "\u76F4\u63A5\u56DE\u590D"
        type: answer
        variables: []
      height: 107
      id: answer
      position:
        x: 980
        y: 282
      positionAbsolute:
        x: 980
        y: 282
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids:
        - 94624cfc-4bb2-417e-adfc-7d5428e093ac
        desc: ''
        query_variable_selector:
        - '1723188302358'
        - sys.query
        retrieval_mode: single
        selected: false
        single_retrieval_config:
          model:
            completion_params: {}
            mode: chat
            name: qwen-plus-chat
            provider: tongyi
        title: "\u77E5\u8BC6\u68C0\u7D22"
        type: knowledge-retrieval
      height: 92
      id: '1723188386218'
      position:
        x: 380
        y: 282
      positionAbsolute:
        x: 380
        y: 282
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 50.66783737898527
      y: 141.64378704610294
      zoom: 0.5708591711720331
