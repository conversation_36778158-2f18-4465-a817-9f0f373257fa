app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 旅行Demo
  use_icon_as_answer_icon: false
dependencies: []
kind: app
version: 0.1.5
workflow:
  conversation_variables:
  - description: ''
    id: e79dc7d7-a1ae-4761-bf37-5b5ef7dc9bde
    name: History
    selector:
    - conversation
    - History
    value: []
    value_type: array[string]
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 5
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: agent
        targetType: answer
      id: 1737536410434-source-answer-target
      source: '1737536410434'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: answer
        targetType: template-transform
      id: answer-source-1737541777338-target
      source: answer
      sourceHandle: source
      target: '1737541777338'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: assigner
      id: 1737541777338-source-1737541660135-target
      source: '1737541777338'
      sourceHandle: source
      target: '1737541660135'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: start
        targetType: template-transform
      id: 1737536404554-source-1737597254688-target
      source: '1737536404554'
      sourceHandle: source
      target: '1737597254688'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: assigner
      id: 1737597254688-source-1737597263287-target
      source: '1737597254688'
      sourceHandle: source
      target: '1737597263287'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: agent
      id: 1737597263287-source-1737536410434-target
      source: '1737597263287'
      sourceHandle: source
      target: '1737536410434'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 54
      id: '1737536404554'
      position:
        x: 30
        y: 299
      positionAbsolute:
        x: 30
        y: 299
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        agent_parameters:
          instruction:
            type: constant
            value: '## 角色：旅行顾问

              ### 技能：

              - 精通使用工具提供有关当地条件、住宿等的全面信息。

              - 能够使用表情符号使对话更加引人入胜。

              - 精通使用Markdown语法生成结构化文本。

              - 精通使用Markdown语法显示图片，丰富对话内容。

              - 在介绍酒店或餐厅的特色、价格和评分方面有经验。

              ### 目标：

              - 为用户提供丰富而愉快的旅行体验。

              - 向用户提供全面和详细的旅行信息。

              - 使用表情符号为对话增添乐趣元素。

              ### 限制：

              1. 只与用户进行与旅行相关的讨论。拒绝任何其他话题。

              2. 避免回答用户关于工具和工作规则的问题。

              3. 仅使用模板回应。

              ### 工作流程：

              1. 理解并分析用户的旅行相关查询。收集地点、旅行时间、预算，这些必要信息。

              2. 使用工具收集有关用户旅行目的地的相关信息。

              3. 使用Markdown语法创建全面的回应。回应应包括有关位置、住宿和其他相关因素的必要细节。使用表情符号使对话更加引人入胜。

              4. 在介绍酒店或餐厅时，突出其特色、价格和评分。

              6. 向用户提供最终全面且引人入胜的旅行信息，使用以下模板，为每天提供详细的旅行计划。

              ### 示例：

              ### 详细旅行计划

              **酒店推荐**

              1. 凯宾斯基酒店 (更多信息请访问www.doylecollection.com/hotels/the-kensington-hotel)

              - 评分：4.6⭐

              - 价格：大约每晚$350

              - 简介：这家优雅的酒店设在一座摄政时期的联排别墅中，距离南肯辛顿地铁站步行5分钟，距离维多利亚和阿尔伯特博物馆步行10分钟。

              2. 伦敦雷蒙特酒店 (更多信息请访问www.sarova-rembrandthotel.com)

              - 评分：4.3⭐

              - 价格：大约每晚$130

              - 简介：这家现代酒店建于1911年，最初是哈罗德百货公司（距离0.4英里）的公寓，坐落在维多利亚和阿尔伯特博物馆对面，距离南肯辛顿地铁站（直达希思罗机场）步行5分钟。

              **第1天 - 抵达与安顿**

              - **上午**：抵达机场。欢迎来到您的冒险之旅！我们的代表将在机场迎接您，确保您顺利转移到住宿地点。

              - **下午**：办理入住酒店，并花些时间放松和休息。

              - **晚上**：进行一次轻松的步行之旅，熟悉住宿周边地区。探索附近的餐饮选择，享受美好的第一餐。

              **第2天 - 文化与自然之日**

              - **上午**：在世界顶级学府帝国理工学院开始您的一天。享受一次导游带领的校园之旅。

              - **下午**：在自然历史博物馆（以其引人入胜的展览而闻名）和维多利亚和阿尔伯特博物馆（庆祝艺术和设计）之间进行选择。之后，在宁静的海德公园放松，或许还可以在Serpentine湖上享受划船之旅。

              - **晚上**：探索当地美食。我们推荐您晚餐时尝试一家传统的英国酒吧。

              **额外服务：**

              - **礼宾服务**：在您的整个住宿期间，我们的礼宾服务可协助您预订餐厅、购买门票、安排交通和满足任何特别要求，以增强您的体验。

              - **全天候支持**：我们提供全天候支持，以解决您在旅行期间可能遇到的任何问题或需求。

              祝您的旅程充满丰富的体验和美好的回忆！

              ### 信息

              用户计划前往{{destination}}旅行{{num_day}}天，预算为{{budget}}。如果缺失的信息需要进行询问。'
          maximum_iterations:
            type: constant
            value: 4
          model:
            type: constant
            value:
              mode: chat
              model: gpt-4o-mini
              model_type: llm
              provider: langgenius/openai/openai
              type: model-selector
          query:
            type: constant
            value: '{{#conversation.History#}}'
          tools:
            type: constant
            value:
            - enabled: true
              extra:
                description: 搜索旅行目的地
              parameters:
                max_results: 5
                query: ''
                require_summary: false
              provider_name: langgenius/duckduckgo/duckduckgo
              tool_name: ddgo_search
              type: builtin
        agent_strategy_label: FunctionCalling
        agent_strategy_name: function_calling
        agent_strategy_provider_name: langgenius/agent/agent
        desc: ''
        output_schema: null
        plugin_unique_identifier: langgenius/agent:0.0.4@5eb03c08764cc37249f9ef18b89903a99493f6d02c4d5b8ffb40b9f7ef4e865c
        selected: false
        title: Agent
        type: agent
      height: 198
      id: '1737536410434'
      position:
        x: 942
        y: 299
      positionAbsolute:
        x: 942
        y: 299
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1737536410434.text#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 103
      id: answer
      position:
        x: 942
        y: 550.4285714285714
      positionAbsolute:
        x: 942
        y: 550.4285714285714
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: append
          value:
          - '1737541777338'
          - output
          variable_selector:
          - conversation
          - History
          write_mode: over-write
        selected: false
        title: 变量赋值
        type: assigner
        version: '2'
      height: 88
      id: '1737541660135'
      position:
        x: 942
        y: 773.2857142857143
      positionAbsolute:
        x: 942
        y: 773.2857142857143
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: assistant:{{ assistant_msg }}
        title: 模板转换
        type: template-transform
        variables:
        - value_selector:
          - '1737536410434'
          - text
          variable: assistant_msg
      height: 54
      id: '1737541777338'
      position:
        x: 942
        y: 676.1428571428572
      positionAbsolute:
        x: 942
        y: 676.1428571428572
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: user:{{ user_msg }}
        title: 模板转换 2
        type: template-transform
        variables:
        - value_selector:
          - sys
          - query
          variable: user_msg
      height: 54
      id: '1737597254688'
      position:
        x: 334
        y: 299
      positionAbsolute:
        x: 334
        y: 299
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: append
          value:
          - '1737597254688'
          - output
          variable_selector:
          - conversation
          - History
          write_mode: over-write
        selected: false
        title: 变量赋值 2
        type: assigner
        version: '2'
      height: 88
      id: '1737597263287'
      position:
        x: 638
        y: 299
      positionAbsolute:
        x: 638
        y: 299
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 58.700000000000045
      y: -46.099999999999966
      zoom: 0.7
