app:
  description: '可以自动化生成全套教程。

    开始时输入想要生成的教程名称。'
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 自动化教程生成-演示版
  use_icon_as_answer_icon: false
kind: app
version: 0.1.2
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: llm
        targetType: code
      id: 1728198088794-source-1728203511475-target
      selected: false
      source: '1728198088794'
      sourceHandle: source
      target: '1728203511475'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        iteration_id: '1728217105363'
        sourceType: code
        targetType: llm
      id: 1728218846720-source-1728218323079-target
      selected: false
      source: '1728218846720'
      sourceHandle: source
      target: '1728218323079'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        sourceType: start
        targetType: llm
      id: 1728198077941-source-1728198088794-target
      selected: false
      source: '1728198077941'
      sourceHandle: source
      target: '1728198088794'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        iteration_id: '1728217105363'
        sourceType: iteration-start
        targetType: code
      id: 1728217105363start-source-1728218846720-target
      selected: false
      source: 1728217105363start
      sourceHandle: source
      target: '1728218846720'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        iteration_id: '1728217105363'
        sourceType: llm
        targetType: code
      id: 1728218323079-source-1728463823248-target
      selected: false
      source: '1728218323079'
      sourceHandle: source
      target: '1728463823248'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        sourceType: code
        targetType: iteration
      id: 1728203511475-source-1728217105363-target
      source: '1728203511475'
      sourceHandle: source
      target: '1728217105363'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: iteration
        targetType: end
      id: 1728217105363-source-1728198201864-target
      source: '1728217105363'
      sourceHandle: source
      target: '1728198201864'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: '👨‍💼作者：北平牧哥

          💚微信：mugeleey

          🗨微信公众号：pekingmuge

          🌍Twitter: https://x.com/pekingmuge'
        selected: true
        title: 开始
        type: start
        variables:
        - label: 教程名称
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: course_name
      height: 180
      id: '1728198077941'
      position:
        x: 30
        y: 295
      positionAbsolute:
        x: 30
        y: 295
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 生成教程大纲，课程目录
        model:
          completion_params:
            temperature: 0
          mode: chat
          name: o1-mini-2024-09-12
          provider: openai
        prompt_template:
        - id: fa51a950-08a7-45bf-936c-14aa386a2ec6
          role: system
          text: 你是一个{{#1728198077941.course_name#}}方面的专家，编写一个{{#1728198077941.course_name#}}教程的课程大纲，并仅以JSON列表的形式输出。
        - id: 4a06912a-7141-497b-a68c-b425d459a97c
          role: user
          text: '编写一个{{#1728198077941.course_name#}}教程大纲，要求从入门到精通。

            整理成列表，并以JSON方式输出结果。'
        - id: 01f88599-5ae2-4978-a69b-1a13cc9b34be
          role: assistant
          text: "输出格式如下（每一章至少5-12篇内容）\n[\n  {\n    \"chapter\": \"第1章\",\n      \"\
            title\": \"xxxx\",\n      \"contents\":\n      [\n          \"1.1 xxx\"\
            ,\n          ...\n      ]\n  },\n  ...\n]"
        selected: false
        title: LLM 生成课程大纲
        type: llm
        variables: []
        vision:
          enabled: false
      height: 124
      id: '1728198088794'
      position:
        x: 333
        y: 295
      positionAbsolute:
        x: 333
        y: 295
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1728198077941'
          - course_name
          variable: course_name
        - value_selector:
          - '1728217105363'
          - output
          variable: course
        selected: false
        title: 结束
        type: end
      height: 114
      id: '1728198201864'
      position:
        x: 2079
        y: 295
      positionAbsolute:
        x: 2079
        y: 295
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        code: "\ndef main(text: str) -> dict:\n    import re, json\n\n    pattern\
          \ = r\"^```json\\n([\\s\\S]*)\\n```\"\n    matched = re.match(pattern, text,\
          \ re.MULTILINE)\n    if not matched:\n        return {\"result\": \"None\"\
          }\n    jsonstr = matched.group(1)\n    obj = json.loads(jsonstr)\n    contents\
          \ = []\n    for chapter in obj:\n        for subtitle in chapter['contents']:\n\
          \            contents += [\n                {\n                    \"chapter\"\
          : chapter[\"chapter\"],\n                    \"title\": chapter[\"title\"\
          ],\n                    \"subtitle\": subtitle,\n                }\n   \
          \         ]\n    return {\n        \"result\": contents,\n    }"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[object]
        selected: false
        title: 转换成JSON
        type: code
        variables:
        - value_selector:
          - '1728198088794'
          - text
          variable: text
      height: 52
      id: '1728203511475'
      position:
        x: 636
        y: 295
      positionAbsolute:
        x: 636
        y: 295
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        height: 190
        iterator_selector:
        - '1728203511475'
        - result
        output_selector:
        - '1728463823248'
        - result
        output_type: array[object]
        selected: false
        start_node_id: 1728217105363start
        title: 迭代生成课程
        type: iteration
        width: 1080
      height: 190
      id: '1728217105363'
      position:
        x: 939
        y: 295
      positionAbsolute:
        x: 939
        y: 295
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1080
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1728217105363start
      parentId: '1728217105363'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 963
        y: 363
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: true
        iteration_id: '1728217105363'
        model:
          completion_params:
            temperature: 0
          mode: chat
          name: gpt-4o-mini
          provider: openai
        prompt_template:
        - id: 42d3ce21-8fa0-4fe3-b28b-3c6807fe8575
          role: system
          text: 你是一个 {{#1728198077941.course_name#}} 专家，正在写一篇关于 {{#1728198077941.course_name#}}
            的教程，每一个知识点要尽可能详尽，字数多一点。
        - id: a40f0999-e68c-4f9a-add9-46ab3cb7bb38
          role: user
          text: 写一篇关于{{#1728218846720.title#}} {{#1728218846720.subtitle#}} 的教程文章，内容要严谨，要有丰富的示例代码。教程还要很详细，到达专家级别，并且要有每一个内容的优点、缺点、注意事项。
        selected: false
        title: LLM生成课程
        type: llm
        variables: []
        vision:
          configs:
            detail: high
          enabled: true
      height: 96
      id: '1728218323079'
      parentId: '1728217105363'
      position:
        x: 427.82204039715634
        y: 65
      positionAbsolute:
        x: 1366.8220403971563
        y: 360
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
      zIndex: 1002
    - data:
        code: "def main(item: object) -> dict:\n    return item\n"
        code_language: python3
        desc: ''
        isInIteration: true
        iteration_id: '1728217105363'
        outputs:
          chapter:
            children: null
            type: string
          subtitle:
            children: null
            type: string
          title:
            children: null
            type: string
        selected: false
        title: 转object
        type: code
        variables:
        - value_selector:
          - '1728217105363'
          - item
          variable: item
      height: 52
      id: '1728218846720'
      parentId: '1728217105363'
      position:
        x: 130.46261275218922
        y: 65
      positionAbsolute:
        x: 1069.4626127521892
        y: 360
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
      zIndex: 1002
    - data:
        code: "\ndef main(course: str, info: str) -> dict:\n    return {\n       \
          \ \"result\": {\n            \"info\": info,\n            \"course\": course,\n\
          \        },\n    }\n"
        code_language: python3
        desc: ''
        isInIteration: true
        iteration_id: '1728217105363'
        outputs:
          result:
            children: null
            type: object
        selected: false
        title: 整合
        type: code
        variables:
        - value_selector:
          - '1728218323079'
          - text
          variable: course
        - value_selector:
          - '1728217105363'
          - item
          variable: info
      height: 52
      id: '1728463823248'
      parentId: '1728217105363'
      position:
        x: 741.8919201860167
        y: 65
      positionAbsolute:
        x: 1680.8919201860167
        y: 360
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
      zIndex: 1002
    - data:
        author: Dify
        desc: ''
        height: 424
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"教程自动生成工作流","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"本工作流实现了生成高效且定制化的学习资料的自动化过程。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"生成教程结果演示地址：https://tutorial.pekingmuge.top","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"--------------","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"本工作流仅为演示版，能够生成完整的教程，相比完整版没有做数据分片及并行处理，但对于初学者入门Dify工作流开发已经够了。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"完整版包含如下功能：","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"1、数据分片。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"2、并行迭代处理，能大幅提高执行效率。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"3、增加了SEO信息，如果想把生成课程用于网站开发将有极大帮助。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"如果希望用到实际网站开发，生成教程网站，可联系作者获取完整版本（会收取少量费用）","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"====================================","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"👨‍💼作者：北平牧哥","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"💚微信：mugeleey","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"🗨微信公众号：pekingmuge","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"🌍Twitter:
          https://x.com/pekingmuge","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: yellow
        title: ''
        type: ''
        width: 412
      height: 424
      id: '1729309839140'
      position:
        x: 30
        y: -151.90242105040247
      positionAbsolute:
        x: 30
        y: -151.90242105040247
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 412
    viewport:
      x: 47.29542160499841
      y: 267.16018942881976
      zoom: 1.0000000000000004
