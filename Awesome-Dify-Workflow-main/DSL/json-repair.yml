app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: json-repair
  use_icon_as_answer_icon: false
kind: app
version: 0.1.2
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: code
      id: 1732007415808-source-1732007419308-target
      source: '1732007415808'
      sourceHandle: source
      target: '1732007419308'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: end
      id: 1732007419308-source-1732007423290-target
      source: '1732007419308'
      sourceHandle: source
      target: '1732007423290'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: llm_string
          max_length: 10000
          options: []
          required: true
          type: paragraph
          variable: llm_string
      height: 90
      id: '1732007415808'
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json_repair\nimport json\n\ndef main(llm_string):\n    new_string\
          \ = json_repair.repair_json(llm_string, ensure_ascii=False)\n    return\
          \ {\n        \"result\": new_string\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: true
        title: 代码执行
        type: code
        variables:
        - value_selector:
          - '1732007415808'
          - llm_string
          variable: llm_string
      height: 54
      id: '1732007419308'
      position:
        x: 384
        y: 282
      positionAbsolute:
        x: 384
        y: 282
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1732007419308'
          - result
          variable: output
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1732007423290'
      position:
        x: 688
        y: 282
      positionAbsolute:
        x: 688
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 132.5007443566236
      y: 97.9169671661925
      zoom: 0.5277768588189832
