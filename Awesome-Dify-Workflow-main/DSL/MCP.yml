app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: MCP
  use_icon_as_answer_icon: false
dependencies: []
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: agent
      id: 1742957989303-source-1742957995972-target
      source: '1742957989303'
      sourceHandle: source
      target: '1742957995972'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: agent
        targetType: answer
      id: 1742957995972-source-answer-target
      source: '1742957995972'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 54
      id: '1742957989303'
      position:
        x: 77.26818344276029
        y: -75.86796899840161
      positionAbsolute:
        x: 77.26818344276029
        y: -75.86796899840161
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        agent_parameters:
          instruction:
            type: constant
            value: 调用MCP服务，查询天气信息。
          mcp_server:
            type: constant
            value: https://router.mcp.so/sse/*********
          model:
            type: constant
            value:
              completion_params: {}
              mode: chat
              model: gpt-4o-mini
              model_type: llm
              provider: langgenius/openai/openai
              type: model-selector
          query:
            type: constant
            value: '{{#sys.query#}}'
          tools:
            type: constant
            value:
            - enabled: true
              extra:
                description: ''
              parameters: {}
              provider_name: time
              schemas:
              - auto_generate: null
                default: '%Y-%m-%d %H:%M:%S'
                form: form
                human_description:
                  en_US: Time format in strftime standard.
                  ja_JP: Time format in strftime standard.
                  pt_BR: Time format in strftime standard.
                  zh_Hans: strftime 标准的时间格式。
                label:
                  en_US: Format
                  ja_JP: Format
                  pt_BR: Format
                  zh_Hans: 格式
                llm_description: null
                max: null
                min: null
                name: format
                options: []
                placeholder: null
                precision: null
                required: false
                scope: null
                template: null
                type: string
              - auto_generate: null
                default: UTC
                form: form
                human_description:
                  en_US: Timezone
                  ja_JP: Timezone
                  pt_BR: Timezone
                  zh_Hans: 时区
                label:
                  en_US: Timezone
                  ja_JP: Timezone
                  pt_BR: Timezone
                  zh_Hans: 时区
                llm_description: null
                max: null
                min: null
                name: timezone
                options:
                - label:
                    en_US: UTC
                    ja_JP: UTC
                    pt_BR: UTC
                    zh_Hans: UTC
                  value: UTC
                - label:
                    en_US: America/New_York
                    ja_JP: America/New_York
                    pt_BR: America/New_York
                    zh_Hans: 美洲/纽约
                  value: America/New_York
                - label:
                    en_US: America/Los_Angeles
                    ja_JP: America/Los_Angeles
                    pt_BR: America/Los_Angeles
                    zh_Hans: 美洲/洛杉矶
                  value: America/Los_Angeles
                - label:
                    en_US: America/Chicago
                    ja_JP: America/Chicago
                    pt_BR: America/Chicago
                    zh_Hans: 美洲/芝加哥
                  value: America/Chicago
                - label:
                    en_US: America/Sao_Paulo
                    ja_JP: America/Sao_Paulo
                    pt_BR: América/São Paulo
                    zh_Hans: 美洲/圣保罗
                  value: America/Sao_Paulo
                - label:
                    en_US: Asia/Shanghai
                    ja_JP: Asia/Shanghai
                    pt_BR: Asia/Shanghai
                    zh_Hans: 亚洲/上海
                  value: Asia/Shanghai
                - label:
                    en_US: Asia/Ho_Chi_Minh
                    ja_JP: Asia/Ho_Chi_Minh
                    pt_BR: Ásia/Ho Chi Minh
                    zh_Hans: 亚洲/胡志明市
                  value: Asia/Ho_Chi_Minh
                - label:
                    en_US: Asia/Tokyo
                    ja_JP: Asia/Tokyo
                    pt_BR: Asia/Tokyo
                    zh_Hans: 亚洲/东京
                  value: Asia/Tokyo
                - label:
                    en_US: Asia/Dubai
                    ja_JP: Asia/Dubai
                    pt_BR: Asia/Dubai
                    zh_Hans: 亚洲/迪拜
                  value: Asia/Dubai
                - label:
                    en_US: Asia/Kolkata
                    ja_JP: Asia/Kolkata
                    pt_BR: Asia/Kolkata
                    zh_Hans: 亚洲/加尔各答
                  value: Asia/Kolkata
                - label:
                    en_US: Asia/Seoul
                    ja_JP: Asia/Seoul
                    pt_BR: Asia/Seoul
                    zh_Hans: 亚洲/首尔
                  value: Asia/Seoul
                - label:
                    en_US: Asia/Singapore
                    ja_JP: Asia/Singapore
                    pt_BR: Asia/Singapore
                    zh_Hans: 亚洲/新加坡
                  value: Asia/Singapore
                - label:
                    en_US: Europe/London
                    ja_JP: Europe/London
                    pt_BR: Europe/London
                    zh_Hans: 欧洲/伦敦
                  value: Europe/London
                - label:
                    en_US: Europe/Berlin
                    ja_JP: Europe/Berlin
                    pt_BR: Europe/Berlin
                    zh_Hans: 欧洲/柏林
                  value: Europe/Berlin
                - label:
                    en_US: Europe/Moscow
                    ja_JP: Europe/Moscow
                    pt_BR: Europe/Moscow
                    zh_Hans: 欧洲/莫斯科
                  value: Europe/Moscow
                - label:
                    en_US: Australia/Sydney
                    ja_JP: Australia/Sydney
                    pt_BR: Australia/Sydney
                    zh_Hans: 澳大利亚/悉尼
                  value: Australia/Sydney
                - label:
                    en_US: Pacific/Auckland
                    ja_JP: Pacific/Auckland
                    pt_BR: Pacific/Auckland
                    zh_Hans: 太平洋/奥克兰
                  value: Pacific/Auckland
                - label:
                    en_US: Africa/Cairo
                    ja_JP: Africa/Cairo
                    pt_BR: Africa/Cairo
                    zh_Hans: 非洲/开罗
                  value: Africa/Cairo
                placeholder: null
                precision: null
                required: false
                scope: null
                template: null
                type: select
              settings:
                format:
                  value: '%Y-%m-%d %H:%M:%S'
                timezone:
                  value: UTC
              tool_label: 获取当前时间
              tool_name: current_time
              type: builtin
        agent_strategy_label: MCP FunctionCalling
        agent_strategy_name: function_calling
        agent_strategy_provider_name: hjlarry/agent/mcp_agent
        desc: ''
        output_schema: null
        plugin_unique_identifier: hjlarry/agent:0.0.1@f42a5a80b1c77fd0655c755b70ad08da47ceb1acc3638cf13a0eb9ed42b3a128
        selected: true
        title: Agent
        type: agent
      height: 198
      id: '1742957995972'
      position:
        x: 77.26818344276029
        y: 141.49174542794128
      positionAbsolute:
        x: 77.26818344276029
        y: 141.49174542794128
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1742957995972.text#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 105
      id: answer
      position:
        x: 92.65944019346313
        y: 522.3998570370943
      positionAbsolute:
        x: 92.65944019346313
        y: 522.3998570370943
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -1.724234777740719
      y: 195.0933881869936
      zoom: 0.6700068920066886
