app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 图文知识库
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/volcengine_maas:0.0.7@f8e44422cfa5b9a6ac1f2d3b43ef1069868efdad1e5cec2590de3f53ceac37b0
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: knowledge-retrieval
      id: 1743994826325-source-1743994832383-target
      source: '1743994826325'
      sourceHandle: source
      target: '1743994832383'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1743994832383-source-1743994891832-target
      source: '1743994832383'
      sourceHandle: source
      target: '1743994891832'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1743994891832-source-answer-target
      selected: false
      source: '1743994891832'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 54
      id: '1743994826325'
      position:
        x: 30
        y: 252.5
      positionAbsolute:
        x: 30
        y: 252.5
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids:
        - 2516f4a8-9329-4845-a914-7965090ee2e2
        desc: ''
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: BAAI/bge-reranker-v2-m3
            provider: langgenius/siliconflow/siliconflow
          score_threshold: null
          top_k: 4
        query_variable_selector:
        - sys
        - query
        retrieval_mode: multiple
        selected: true
        title: 知识检索
        type: knowledge-retrieval
      height: 92
      id: '1743994832383'
      position:
        x: 334
        y: 252.5
      positionAbsolute:
        x: 334
        y: 252.5
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1743994891832.text#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 105
      id: answer
      position:
        x: 942
        y: 252.5
      positionAbsolute:
        x: 942
        y: 252.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1743994832383'
          - result
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: deepseek-v3-241226
          provider: langgenius/volcengine_maas/volcengine_maas
        prompt_template:
        - id: c96a41bd-c775-4c1d-bcd9-1280033c9ecc
          role: system
          text: '你是一个AI聊天助手，请参考<info></info>中的内容，回答用户的问题。注意保留图片信息。


            <info>

            {{#context#}}

            </info>'
        selected: false
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1743994891832'
      position:
        x: 638
        y: 252.5
      positionAbsolute:
        x: 638
        y: 252.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 104.20000000000005
      y: -21.500000000000057
      zoom: 0.7
