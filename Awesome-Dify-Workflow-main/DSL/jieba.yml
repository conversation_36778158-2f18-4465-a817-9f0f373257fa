app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: jieba
  use_icon_as_answer_icon: false
kind: app
version: 0.1.3
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: code
      id: 1732240791951-source-1732240797960-target
      source: '1732240791951'
      sourceHandle: source
      target: '1732240797960'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: answer
      id: 1732240797960-source-answer-target
      source: '1732240797960'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 54
      id: '1732240791951'
      position:
        x: 30
        y: 258
      positionAbsolute:
        x: 30
        y: 258
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1732240797960.output#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 103
      id: answer
      position:
        x: 638
        y: 258
      positionAbsolute:
        x: 638
        y: 258
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import jieba\nimport logging\njieba.setLogLevel(logging.INFO)\n\ndef\
          \ main(input_content: str):\n    seg_list = jieba.cut(input_content,use_paddle=True)\
          \ # 使用paddle模式\n\n    return {\n        \"output\": str(list(seg_list)),\n\
          \    }"
        code_language: python3
        desc: ''
        outputs:
          output:
            children: null
            type: string
        selected: false
        title: 代码执行
        type: code
        variables:
        - value_selector:
          - sys
          - query
          variable: input_content
      height: 54
      id: '1732240797960'
      position:
        x: 334
        y: 258
      positionAbsolute:
        x: 334
        y: 258
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 265.00000000000006
      y: 65.35000000000008
      zoom: 0.7
