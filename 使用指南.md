# AI英译中PDF翻译器 - 使用指南

## 快速开始

### 1. 导入Workflow

#### 方法一：JSON导入（推荐）
1. 登录Dify平台
2. 创建新的Workflow应用
3. 选择"导入DSL"
4. 上传 `ai_translation_workflow.json` 文件
5. 点击"导入"完成

#### 方法二：YAML导入
1. 在Dify中创建新Workflow
2. 使用 `ai_translation_workflow.yml` 作为参考
3. 手动配置各个节点（适合自定义修改）

### 2. 模型配置

#### 必需模型
- **GPT-4o**（推荐）或 **Claude-3.5-Sonnet**
- 确保模型支持长上下文（至少8K tokens）

#### 模型参数设置
```yaml
术语识别节点:
  temperature: 0.3  # 确保术语翻译一致性
  
初步翻译节点:
  temperature: 0.7  # 保持翻译的自然性
  
翻译审查节点:
  temperature: 0.5  # 平衡准确性和流畅性
  
最终翻译节点:
  temperature: 0.3  # 确保最终结果的精确性
```

### 3. 依赖安装

确保Dify环境中安装了以下Python包：

```bash
pip install PyPDF2 reportlab
```

如果使用Docker部署，在Dockerfile中添加：
```dockerfile
RUN pip install PyPDF2 reportlab
```

## 详细配置

### 文件上传设置

```yaml
file_upload:
  allowed_file_extensions: [".pdf", ".PDF"]
  allowed_file_types: ["document"]
  file_size_limit: 50  # MB
  workflow_file_upload_limit: 1
```

### 文本分块参数

在 `text_chunk_node` 中可以调整：

```python
max_chunk_size = 2000  # 每块最大字符数
overlap_size = 200     # 重叠字符数
```

**调整建议：**
- 短文档：减小chunk_size到1000-1500
- 长文档：保持2000或增加到2500
- 技术文档：增加overlap_size到300-400

### 专业术语库自定义

在 `terminology_extraction` 节点的提示词中添加特定领域术语：

```text
重点关注以下类型的术语：
- 机器学习算法和模型
- 深度学习架构  
- 神经网络组件
- AI技术概念
- 数据处理术语
- 评估指标
- 框架和工具名称
- [添加您的特定领域术语]
```

## 使用流程

### 步骤1：准备PDF文件

**支持的文件类型：**
- ✅ 文本型PDF（可复制文字）
- ❌ 扫描型PDF（图片格式）
- ❌ 加密PDF

**文件要求：**
- 大小：≤ 50MB
- 语言：英文
- 内容：AI/ML相关技术文档

### 步骤2：上传和处理

1. 在Dify应用界面点击"上传文件"
2. 选择您的英文PDF文件
3. 点击"开始翻译"
4. 等待处理完成（时间取决于文档长度）

### 步骤3：下载结果

处理完成后，您将获得：
- 中文PDF文件（Base64编码）
- 翻译状态报告
- 文件大小信息

## 高级配置

### 1. 自定义提示词

#### 术语识别提示词优化
```text
你是一名专业的[特定领域]术语专家。请识别以下英文文本中的专业术语...

特别关注：
- [领域1]相关术语
- [领域2]相关术语
- 新兴技术术语
- 标准化组织定义的术语
```

#### 翻译质量提示词优化
```text
翻译要求：
1. 准确传达原文的技术含义
2. 使用规范的中文学术表达
3. 保持[特定风格]的语言风格
4. 遵循[特定标准]的术语规范
```

### 2. 性能优化

#### 处理大文件
```python
# 在text_chunk_node中调整
max_chunk_size = 1500  # 减小块大小
overlap_size = 150     # 减小重叠

# 或者使用并行处理（需要修改workflow结构）
```

#### 提高翻译速度
- 使用更快的模型（如GPT-3.5-turbo）
- 减少审查步骤（直接从初步翻译到最终输出）
- 调整温度参数以减少生成时间

### 3. 质量控制

#### 添加人工审核节点
在最终PDF生成前添加人工确认步骤：

```yaml
- data:
    type: human-feedback
    title: "人工审核"
    desc: "请审核翻译质量并确认"
```

#### 术语一致性检查
添加术语一致性验证节点：

```python
def check_terminology_consistency(translated_text, terminology_dict):
    # 检查术语翻译的一致性
    inconsistencies = []
    # 实现检查逻辑
    return inconsistencies
```

## 故障排除

### 常见问题及解决方案

#### 1. PDF提取失败
**问题：** "PDF提取失败: ..."
**解决方案：**
- 确认PDF是文本型而非扫描型
- 检查PDF是否加密
- 尝试使用PDF编辑器重新保存

#### 2. 翻译质量不佳
**问题：** 术语翻译不准确或不一致
**解决方案：**
- 检查模型配置（推荐GPT-4o）
- 调整温度参数
- 自定义术语库
- 增加审查步骤

#### 3. 处理时间过长
**问题：** 大文件处理超时
**解决方案：**
- 减小文档大小
- 调整分块参数
- 使用更快的模型
- 分段处理

#### 4. 格式丢失
**问题：** 输出PDF格式不理想
**解决方案：**
- 调整PDF生成代码
- 使用更复杂的格式保持逻辑
- 手动后处理

### 调试技巧

#### 1. 启用详细日志
在代码节点中添加调试输出：

```python
print(f"处理文本长度: {len(text)}")
print(f"分块数量: {len(chunks)}")
```

#### 2. 分步测试
- 单独测试PDF提取功能
- 验证文本分块效果
- 检查术语识别结果
- 测试单个翻译块

#### 3. 性能监控
监控各节点的执行时间和资源使用：

```python
import time
start_time = time.time()
# 处理逻辑
end_time = time.time()
print(f"处理时间: {end_time - start_time}秒")
```

## 最佳实践

### 1. 文档预处理
- 清理不必要的页眉页脚
- 确保文档结构清晰
- 预先标记重要术语

### 2. 批量处理
- 对于多个文档，建议分批处理
- 保持术语翻译的一致性
- 建立项目级术语库

### 3. 质量保证
- 重要文档建议人工校对
- 建立翻译质量评估标准
- 收集用户反馈持续改进

### 4. 版本管理
- 保存不同版本的workflow配置
- 记录重要的参数调整
- 备份自定义术语库

---

如有其他问题，请参考完整的README文档或联系技术支持。
